@echo off
title VMS Production System - Port 8080
color 0A

echo ================================================================
echo                VMS PRODUCTION SYSTEM STARTUP
echo                    PORT 8080 ONLY
echo ================================================================
echo Starting VMS Production System - Clean Architecture...
echo.

REM Check if we're in the correct directory
if not exist "Server" (
    echo ERROR: Server directory not found!
    echo Please run this script from the VMS root directory.
    pause
    exit /b 1
)

if not exist "Client" (
    echo ERROR: Client directory not found!
    echo Please run this script from the VMS root directory.
    pause
    exit /b 1
)

echo [1/4] Checking MySQL Service...
sc query MySQL80 | find "RUNNING" >nul
if %errorlevel% neq 0 (
    echo WARNING: MySQL80 service is not running!
    echo Please start MySQL service manually and try again.
    pause
    exit /b 1
) else (
    echo ✓ MySQL80 service is running
)

echo.
echo [2/4] Building Client for Production...
cd Client
call npm run build
if %errorlevel% neq 0 (
    echo ERROR: Client build failed!
    pause
    exit /b 1
)
echo ✓ Client built successfully and deployed to server/public

echo.
echo [3/4] Building Production Server...
cd ..\Server
call npm run build:prod
if %errorlevel% neq 0 (
    echo ERROR: Server build failed!
    pause
    exit /b 1
)
echo ✓ Server built successfully

echo.
echo [4/4] Starting Single Production System...
echo ✓ Starting VMS on Port 8080 (Client + Server + API + WebSocket)
start "VMS Single System" cmd /k "set NODE_ENV=production && set PREFERRED_PORT=8080 && set DB_HOST=localhost && set DB_PORT=3306 && set DB_NAME=vms_production && node dist/index.js"
timeout /t 5 /nobreak >nul

echo.
echo ================================================================
echo                VMS PRODUCTION SYSTEM READY
echo ================================================================
echo.
echo ✓ System URL: http://localhost:8080
echo ✓ Client: Served from port 8080
echo ✓ Server API: http://localhost:8080/api
echo ✓ WebSocket: http://localhost:8080/socket.io
echo ✓ Health Check: http://localhost:8080/health
echo ✓ Database: vms_production (MySQL)
echo.
echo DEVELOPMENT WORKFLOW:
echo 1. Edit code in /Client/src/
echo 2. Run: cd Client && npm run build
echo 3. Refresh browser at http://localhost:8080
echo.
echo Press any key to open VMS in your browser...
pause >nul

start http://localhost:8080

echo.
echo VMS Production System startup complete!
echo Clean architecture running on port 8080!
echo.
pause
