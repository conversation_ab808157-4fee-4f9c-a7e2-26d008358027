import { StateCreator } from 'zustand';
import { AppState } from '../types';
import { formatCurrentDate } from '../utils';
import { TransactionStatus, Voucher } from '../../types';
import { vouchersApi } from '@/lib/api';
import { offlineAPI } from '@/lib/offline-api';
import { sendStateUpdate } from '@/lib/socket';

export interface VouchersSlice {
  vouchers: AppState['vouchers'];
  addVoucher: AppState['addVoucher'];
  addVoucherToStore: (voucher: Voucher) => void; // NEW: Add existing voucher to store
  updateVoucher: AppState['updateVoucher'];
  deleteVoucher: AppState['deleteVoucher'];
  fetchVouchers: (department?: string) => Promise<boolean>;
  sendVoucherToAudit: (voucherId: string) => Promise<boolean>;
  returnVoucher: (voucherId: string, returnComment: string) => Promise<boolean>;
  certifyVoucher: (voucherId: string) => Promise<boolean>;
  rejectVoucher: (voucherId: string, comment: string) => Promise<boolean>;
}

export const createVouchersSlice: StateCreator<AppState, [], [], VouchersSlice> = (set, get) => ({
  vouchers: [],
  addVoucher: async (voucherData) => {
    try {
      // ARCHITECTURAL FIX: Always use API-based voucher creation for consistency
      // This ensures all vouchers have proper UUID-based IDs and are persisted to database

      // PRODUCTION: Add idempotency key to prevent duplicates
      const idempotencyKey = `${voucherData.department}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const voucherDataWithKey = {
        ...voucherData,
        idempotencyKey
      };

      console.log('🔄 [VOUCHER SLICE] PRODUCTION: Creating voucher with data:', voucherDataWithKey);
      console.log('🔄 [VOUCHER SLICE] PRODUCTION: Data type:', typeof voucherDataWithKey);
      console.log('🔄 [VOUCHER SLICE] PRODUCTION: Data keys:', Object.keys(voucherDataWithKey || {}));
      console.log('🔄 [VOUCHER SLICE] PRODUCTION: Data stringified:', JSON.stringify(voucherDataWithKey));

      // Validate voucher data before API call
      if (!voucherDataWithKey.claimant || !voucherDataWithKey.description || !voucherDataWithKey.amount || !voucherDataWithKey.department) {
        throw new Error('Missing required voucher fields: claimant, description, amount, and department are required');
      }

      // Call offline-aware API to create voucher
      const newVoucher = await offlineAPI.createVoucher(voucherDataWithKey);

      // ARCHITECTURAL FIX: Validate response has UUID-based ID
      if (!newVoucher.id || typeof newVoucher.id !== 'string' || !newVoucher.id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
        console.error('❌ CRITICAL: API returned invalid voucher ID format:', newVoucher.id);
        throw new Error('Server returned invalid voucher ID format. Please contact system administrator.');
      }

      console.log('✅ VOUCHER CREATED WITH VALID UUID:', newVoucher.id);

      // PRODUCTION: Add voucher to store only if it doesn't exist
      set((state) => {
        const existingVoucher = state.vouchers.find(v =>
          v.id === newVoucher.id ||
          v.voucherId === newVoucher.voucher_id ||
          (v.voucher_id && v.voucher_id === newVoucher.voucher_id) ||
          (v.voucherId === newVoucher.voucherId)
        );

        if (existingVoucher) {
          console.log(`⚠️ DUPLICATE VOUCHER PREVENTED: ${newVoucher.voucher_id || newVoucher.voucherId}`);
          return state; // Return unchanged state to prevent duplicate
        }

        const updatedVouchers = [...state.vouchers, newVoucher];
        console.log(`✅ PRODUCTION VOUCHER ADDED TO STORE: ${newVoucher.voucher_id || newVoucher.voucherId} (${newVoucher.id})`);
        console.log(`📊 Total vouchers in store: ${updatedVouchers.length}`);
        console.log(`🔍 Pending vouchers for ${newVoucher.department}:`,
          updatedVouchers.filter(v =>
            v.department === newVoucher.department &&
            (v.status === "PENDING SUBMISSION" || v.status === "PENDING") &&
            !v.sentToAudit &&
            !v.deleted
          ).length
        );
        return { vouchers: updatedVouchers };
      });

      // REMOVED: State update that was causing infinite loops
      // The server already handles WebSocket broadcasts

      return newVoucher;
    } catch (error: any) {
      console.error('❌ [VOUCHER SLICE] Error creating voucher:', error);
      console.error('❌ [VOUCHER SLICE] Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        config: error.config
      });
      throw error;
    }
  },
  updateVoucher: async (voucherId, voucherData) => {
    try {
    // Get current voucher for comparison
    const currentVoucher = get().vouchers.find(v => v.id === voucherId);

    // Create a deep copy of the data with consistent string comments to avoid reference issues
    const cleanedData = JSON.parse(JSON.stringify(voucherData));

    // Ensure comments are always strings if present
    if (voucherData.returnComment !== undefined) {
      cleanedData.returnComment = String(voucherData.returnComment);
      console.log(`Normalized returnComment to string: "${cleanedData.returnComment}"`);
    }

    if (voucherData.comment !== undefined) {
      cleanedData.comment = String(voucherData.comment);
      console.log(`Normalized comment to string: "${cleanedData.comment}"`);
    }

    // Clean up numeric fields
    if (voucherData.preAuditedAmount !== undefined) {
      if (typeof voucherData.preAuditedAmount === 'string') {
        const parsed = parseFloat(voucherData.preAuditedAmount);
        if (!isNaN(parsed)) {
          cleanedData.preAuditedAmount = parsed;
          console.log(`Converted preAuditedAmount from string to number: ${parsed}`);
        } else {
          console.warn(`Invalid preAuditedAmount: ${voucherData.preAuditedAmount}`);
          delete cleanedData.preAuditedAmount;
        }
      }
    }

    if (voucherData.taxAmount !== undefined) {
      if (typeof voucherData.taxAmount === 'string') {
        const parsed = parseFloat(voucherData.taxAmount);
        if (!isNaN(parsed)) {
          cleanedData.taxAmount = parsed;
          console.log(`Converted taxAmount from string to number: ${parsed}`);
        } else {
          console.warn(`Invalid taxAmount: ${voucherData.taxAmount}`);
          delete cleanedData.taxAmount;
        }
      }
    }

    // Ensure status is explicitly set for edited vouchers if status is in AUDIT: PROCESSING
    if (cleanedData.status === "AUDIT: PROCESSING") {
      console.log("Ensuring voucher will appear in PENDING DISPATCH tab");
      cleanedData.dispatched = false;
    }

    // Special handling for rejected vouchers
    if (cleanedData.status === "VOUCHER REJECTED") {
      console.log(`Applying REJECTED status to voucher ${voucherId}`);

      // Force these critical properties for rejected vouchers
      cleanedData.deleted = false; // Critical: Never mark rejected vouchers as deleted
      cleanedData.sentToAudit = true; // Keep this flag to ensure it appears in filters

      if (!cleanedData.rejectionTime) {
        cleanedData.rejectionTime = formatCurrentDate();
      }

      console.log(`Final rejected voucher data for ${voucherId}:`, cleanedData);
    }

    // Special handling for vouchers
    // First, ensure we have a string for the comment
    let commentText = "NO COMMENT PROVIDED";

    if (typeof cleanedData.returnComment === 'string' && cleanedData.returnComment.trim().length > 0) {
      commentText = cleanedData.returnComment;
    } else if (typeof cleanedData.comment === 'string' && cleanedData.comment.trim().length > 0) {
      commentText = cleanedData.comment;
    }

    // Case 1: Voucher is explicitly marked as returned
    if (cleanedData.status === "VOUCHER RETURNED" || cleanedData.isReturned === true) {
      console.log(`Applying RETURNED status to voucher ${voucherId}`);

      // Force these critical properties for returned vouchers
      cleanedData.isReturned = true;
      cleanedData.pendingReturn = false; // Clear pending flag when fully returned

      if (!cleanedData.returnTime) {
        cleanedData.returnTime = formatCurrentDate();
      }

      // Make sure both returnComment and comment are properly set as strings
      cleanedData.returnComment = commentText;
      cleanedData.comment = commentText;

      console.log(`Final returned voucher data for ${voucherId}:`, {
        isReturned: cleanedData.isReturned,
        pendingReturn: cleanedData.pendingReturn,
        status: cleanedData.status,
        returnComment: cleanedData.returnComment,
        comment: cleanedData.comment
      });
    }

    // Case 2: Voucher is marked for pending return
    else if (cleanedData.pendingReturn === true) {
      console.log(`Applying PENDING RETURN status to voucher ${voucherId}`);

      // For pending return, ensure isReturned is false
      cleanedData.isReturned = false;
      cleanedData.pendingReturn = true;

      // Set status to AUDIT: PROCESSING to ensure it appears in PENDING DISPATCH
      if (!cleanedData.status || cleanedData.status !== "AUDIT: PROCESSING") {
        cleanedData.status = "AUDIT: PROCESSING";
      }

      if (!cleanedData.returnTime) {
        cleanedData.returnTime = formatCurrentDate();
      }

      // Make sure both returnComment and comment are properly set as strings
      cleanedData.returnComment = commentText;
      cleanedData.comment = commentText;

      console.log(`Final pending return voucher data for ${voucherId}:`, {
        pendingReturn: cleanedData.pendingReturn,
        isReturned: cleanedData.isReturned,
        status: cleanedData.status,
        returnComment: cleanedData.returnComment,
        comment: cleanedData.comment
      });
    }

    // ARCHITECTURAL FIX: Always use API-based voucher updates for consistency
    // Call API to update voucher
    const updatedVoucher = await vouchersApi.updateVoucher(voucherId, cleanedData);

      // Update local state with change detection
      set((state) => {
        const existingVoucher = state.vouchers.find(v => v.id === voucherId);
        if (!existingVoucher) {
          console.error(`Voucher ${voucherId} not found for API update`);
          return state;
        }

        // Check if there are actual changes
        const hasChanges = Object.keys(updatedVoucher).some(key => {
          return existingVoucher[key as keyof typeof existingVoucher] !== updatedVoucher[key as keyof typeof updatedVoucher];
        });

        if (!hasChanges) {
          console.log(`No changes detected for voucher ${voucherId} from API, skipping update`);
          return state;
        }

        return {
          vouchers: state.vouchers.map(voucher =>
            voucher.id === voucherId ? updatedVoucher : voucher
          )
        };
      });

      // Send state update to other clients
      sendStateUpdate({
        type: 'VOUCHER_UPDATE',
        voucher: updatedVoucher
      });

      // Handle notifications for status changes
      const voucherForNotification = get().vouchers.find(v => v.id === voucherId);
      if (voucherForNotification) {
        // Handle notification for rejection
        if (cleanedData.status === "VOUCHER REJECTED") {
          const voucher = get().vouchers.find(v => v.id === voucherId);
          if (voucher) {
            const departmentUser = get().users.find(u => u.department === voucher.department);
            if (departmentUser) {
              const currentTime = formatCurrentDate();
              const commentText = typeof cleanedData.comment === 'string' ? cleanedData.comment : 'NO COMMENT PROVIDED';

              // Notify department about the rejection
              get().addNotification({
                userId: departmentUser.id,
                message: `VOUCHER ${voucher.voucherId} REJECTED BY AUDIT: ${commentText}`,
                voucherId,
                type: "VOUCHER_REJECTED"
              });
            }
          }
        }

        // If status changed to certified, notify department
        if (cleanedData.status === "VOUCHER CERTIFIED") {
          const voucher = get().vouchers.find(v => v.id === voucherId);
          if (voucher) {
            const departmentUser = get().users.find(u => u.department === voucher.department);
            if (departmentUser) {
              get().addNotification({
                userId: departmentUser.id,
                message: `VOUCHER ${voucher.voucherId} CERTIFIED BY AUDIT`,
                voucherId,
                type: "VOUCHER_CERTIFIED"
              });
            }
          }
        }

        // If voucher is marked as returned or has status VOUCHER RETURNED, notify department
        if (cleanedData.isReturned === true || cleanedData.status === "VOUCHER RETURNED") {
          const voucher = get().vouchers.find(v => v.id === voucherId);
          if (voucher) {
            const departmentUser = get().users.find(u => u.department === voucher.department);
            if (departmentUser) {
              // Extract comment ensuring it's a string
              let commentText = "NO COMMENT PROVIDED";

              if (typeof cleanedData.returnComment === 'string' && cleanedData.returnComment.trim().length > 0) {
                commentText = cleanedData.returnComment;
              } else if (typeof cleanedData.comment === 'string' && cleanedData.comment.trim().length > 0) {
                commentText = cleanedData.comment;
              }

              // Create notification with the comment
              const notificationMessage = `VOUCHER ${voucher.voucherId} RETURNED BY AUDIT: ${commentText}`;

              get().addNotification({
                userId: departmentUser.id,
                message: notificationMessage,
                voucherId,
                type: "VOUCHER_RETURNED"
              });
            }
          }
        }
      }
    } catch (error) {
    console.error('Error updating voucher:', error);
    throw error;
  }
},
  deleteVoucher: async (voucherId) => {
    try {
    // Check if the voucher exists
    const voucher = get().vouchers.find(v => v.id === voucherId);
    if (!voucher) {
      console.log(`Voucher with ID: ${voucherId} not found`);
      return;
    }

    // CRITICAL FIX: For rejected vouchers, use API to persist deletion to database
    if (voucher.status === "VOUCHER REJECTED") {
      console.log(`API-based deletion of rejected voucher ${voucherId}`);

      // Call API to persist deletion to database
      await vouchersApi.deleteVoucher(voucherId);

      // Update local state
      set((state) => ({
        vouchers: state.vouchers.map(v =>
          v.id === voucherId ? { ...v, deleted: true } : v
        )
      }));

      // Send state update to other clients
      sendStateUpdate({
        type: 'VOUCHER_DELETE',
        voucherId
      });
      return;
    }

    // ARCHITECTURAL FIX: Always use API-based voucher deletion for consistency
    // This ensures deletion is persisted to database and broadcasted to all clients
    console.log(`API-based deletion of voucher with ID: ${voucherId}`);

    // Call API to delete voucher
    await vouchersApi.deleteVoucher(voucherId);

    // Update local state
    set((state) => ({
      vouchers: state.vouchers.filter(voucher => voucher.id !== voucherId)
    }));

    // Send state update to other clients
    sendStateUpdate({
      type: 'VOUCHER_DELETE',
      voucherId
    });
  } catch (error) {
    console.error('Error deleting voucher:', error);
    throw error;
  }
},

// Fetch vouchers from API
fetchVouchers: async (department?: string) => {
  try {
    const currentUser = get().currentUser;
    console.log(`Fetching vouchers for department: ${department || (currentUser?.department || 'all')}`);

    // If department is not specified, use the current user's department
    const deptToFetch = department || (currentUser?.department || 'all');

    // Get current vouchers before fetching
    const currentVouchers = get().vouchers;
    console.log(`Current voucher count before fetch: ${currentVouchers.length}`);

    // Call API to get vouchers with a timestamp to prevent caching
    const vouchers = await vouchersApi.getAllVouchers(deptToFetch, Date.now());

    console.log(`Received ${vouchers.length} vouchers from API for department ${deptToFetch}`);

    // Check if we're missing any vouchers
    if (currentVouchers.length > 0 && vouchers.length > 0) {
      // Get all voucher IDs from current and new vouchers
      const currentIds = new Set(currentVouchers.map(v => v.id));
      const newIds = new Set(vouchers.map(v => v.id));

      // Find missing vouchers
      const missingFromCurrent = [...newIds].filter(id => !currentIds.has(id));
      const missingFromNew = [...currentIds].filter(id => !newIds.has(id) &&
        // Only consider vouchers from the same department or if we're fetching all
        (deptToFetch === 'all' || currentVouchers.find(v => v.id === id)?.department === deptToFetch));

      console.log(`Missing from current: ${missingFromCurrent.length}, Missing from new: ${missingFromNew.length}`);

      if (missingFromCurrent.length > 0) {
        console.log('New vouchers found that were missing from current state:',
          missingFromCurrent.map(id => {
            const v = vouchers.find(v => v.id === id);
            return v ? `${v.voucherId || v.voucher_id} (${v.department})` : id;
          }));
      }

      if (missingFromNew.length > 0) {
        console.log('Vouchers missing from API response that were in current state:',
          missingFromNew.map(id => {
            const v = currentVouchers.find(v => v.id === id);
            return v ? `${v.voucherId || v.voucher_id} (${v.department})` : id;
          }));
      }
    }

    // COMPREHENSIVE SOLUTION: Normalize field names and fix data types
    const normalizedVouchers = vouchers.map(voucher => ({
      ...voucher,
      voucherId: voucher.voucherId || voucher.voucher_id,
      originalDepartment: voucher.originalDepartment || voucher.original_department,  // 🔧 CRITICAL FIX: Add missing originalDepartment normalization
      sentToAudit: Boolean(voucher.sentToAudit || voucher.sent_to_audit),  // Explicit boolean conversion
      dispatchedBy: voucher.dispatchedBy || voucher.dispatched_by,
      dispatchTime: voucher.dispatchTime || voucher.dispatch_time,
      receivedBy: voucher.receivedBy || voucher.received_by,
      receiptTime: voucher.receiptTime || voucher.receipt_time,
      receivedByAudit: Boolean(voucher.receivedByAudit || voucher.received_by_audit),  // CRITICAL FIX: Add missing field normalization
      certifiedBy: voucher.certifiedBy || voucher.certified_by,
      auditDispatchTime: voucher.auditDispatchTime || voucher.audit_dispatch_time,
      auditDispatchedBy: voucher.auditDispatchedBy || voucher.audit_dispatched_by,
      batchId: voucher.batchId || voucher.batch_id,
      isReturned: Boolean(voucher.isReturned || voucher.is_returned),
      returnComment: voucher.returnComment || voucher.return_comment,
      returnTime: voucher.returnTime || voucher.return_time,
      pendingReturn: Boolean(voucher.pendingReturn || voucher.pending_return),
      departmentReceiptTime: voucher.departmentReceiptTime || voucher.department_receipt_time,
      departmentReceivedBy: voucher.departmentReceivedBy || voucher.department_received_by,
      rejectedBy: voucher.rejectedBy || voucher.rejected_by,
      rejectionTime: voucher.rejectionTime || voucher.rejection_time,
      preAuditedAmount: voucher.preAuditedAmount || voucher.pre_audited_amount,
      preAuditedBy: voucher.preAuditedBy || voucher.pre_audited_by,
      taxType: voucher.taxType || voucher.tax_type,
      taxDetails: voucher.taxDetails || voucher.tax_details,
      taxAmount: voucher.taxAmount || voucher.tax_amount,
      dispatchToAuditBy: voucher.dispatchToAuditBy || voucher.dispatch_to_audit_by,
      createdBy: voucher.createdBy || voucher.created_by,
      workStarted: Boolean(voucher.workStarted || voucher.work_started), // CRITICAL FIX: Add missing workStarted normalization
      dispatched: Boolean(voucher.dispatched), // CRITICAL FIX: Add missing dispatched normalization
      deleted: Boolean(voucher.deleted)
    }));

    // Log the first few vouchers for debugging
    if (normalizedVouchers.length > 0) {
      console.log('First few normalized vouchers:', normalizedVouchers.slice(0, 3).map(v => ({
        id: v.id,
        voucherId: v.voucherId,
        department: v.department,
        status: v.status,
        sentToAudit: v.sentToAudit,
        deleted: v.deleted
      })));
    } else if (deptToFetch !== 'AUDIT') {
      console.warn(`No vouchers returned for department: ${deptToFetch}`);
    }

    // Update local state with normalized data
    set({ vouchers: normalizedVouchers });

    // Log success
    console.log(`Successfully updated store with ${vouchers.length} vouchers for department ${deptToFetch}`);

    return true;
  } catch (error) {
    console.error('Error fetching vouchers:', error);
    return false;
  }
},

// Send voucher to audit
sendVoucherToAudit: async (voucherId) => {
  try {
    // Call API to send voucher to audit
    const updatedVoucher = await vouchersApi.sendToAudit(voucherId);

    // Update local state
    set((state) => ({
      vouchers: state.vouchers.map(voucher =>
        voucher.id === voucherId ? updatedVoucher : voucher
      )
    }));

    // REMOVED: Redundant state update that was causing infinite loops
    // The server already broadcasts WebSocket events

    // PRODUCTION-READY: Force complete data refresh after individual operations
    console.log('🔄 FORCING COMPLETE DATA REFRESH after individual send to audit...');
    try {
      const { fetchVouchers, fetchBatches, currentUser } = get();
      if (currentUser?.department) {
        // Add a small delay to ensure database has been updated
        await new Promise(resolve => setTimeout(resolve, 500));

        // Force refresh with timestamp to bypass cache
        await fetchVouchers(currentUser.department);
        await fetchBatches();

        console.log('✅ COMPLETE DATA REFRESH completed successfully after individual operation');
      }
    } catch (refreshError) {
      console.error('❌ Error during complete data refresh:', refreshError);
      // Continue execution - don't fail the entire operation
    }

    return true;
  } catch (error) {
    console.error('Error sending voucher to audit:', error);
    return false;
  }
},

// Return voucher
returnVoucher: async (voucherId, returnComment) => {
  try {
    // Call API to return voucher
    const updatedVoucher = await vouchersApi.returnVoucher(voucherId, returnComment);

    // Update local state
    set((state) => ({
      vouchers: state.vouchers.map(voucher =>
        voucher.id === voucherId ? updatedVoucher : voucher
      )
    }));

    // Send state update to other clients
    sendStateUpdate({
      type: 'VOUCHER_UPDATE',
      voucher: updatedVoucher
    });

    return true;
  } catch (error) {
    console.error('Error returning voucher:', error);
    return false;
  }
},

// Certify voucher
certifyVoucher: async (voucherId) => {
  try {
    // Call API to certify voucher
    const updatedVoucher = await vouchersApi.certifyVoucher(voucherId);

    // Update local state
    set((state) => ({
      vouchers: state.vouchers.map(voucher =>
        voucher.id === voucherId ? updatedVoucher : voucher
      )
    }));

    // Send state update to other clients
    sendStateUpdate({
      type: 'VOUCHER_UPDATE',
      voucher: updatedVoucher
    });

    return true;
  } catch (error) {
    console.error('Error certifying voucher:', error);
    return false;
  }
},

// UPDATED: Reject voucher using new workflow API
rejectVoucher: async (voucherId, comment) => {
  try {
    // Import the new workflow API
    const { voucherWorkflowApi } = await import('../api/voucher-workflow.api');

    // Call new workflow API to reject voucher
    const result = await voucherWorkflowApi.rejectVoucher({
      voucherId,
      rejectionReason: comment
    });

    if (!result.success) {
      throw new Error(result.message || 'Failed to reject voucher');
    }

    // Refresh vouchers to get updated state
    await get().fetchVouchers();

    // Send state update to other clients
    sendStateUpdate({
      type: 'VOUCHER_WORKFLOW_UPDATE',
      action: 'REJECT',
      voucherId,
      result
    });

    return true;
  } catch (error) {
    console.error('Error rejecting voucher:', error);
    return false;
  }
},
// WebSocket update handler with improved change detection
updateVoucherFromWebSocket: (voucherId: string, updatedVoucher: Voucher) => {
  set((state) => {
    const existingVoucher = state.vouchers.find(v => v.id === voucherId);
    if (!existingVoucher) {
      return { vouchers: [...state.vouchers, updatedVoucher] };
    }

    // CRITICAL FIX: Always update for audit-related fields to ensure real-time tab updates
    const auditFields = ['preAuditedAmount', 'preAuditedBy', 'certifiedBy', 'workStarted', 'comment', 'pendingReturn', 'dispatched'];
    const hasAuditChanges = auditFields.some(field => {
      const oldValue = existingVoucher[field as keyof typeof existingVoucher];
      const newValue = updatedVoucher[field as keyof typeof updatedVoucher];
      return oldValue !== newValue;
    });

    // Check if there are actual changes to prevent unnecessary re-renders
    const hasChanges = Object.keys(updatedVoucher).some(key => {
      return existingVoucher[key as keyof typeof existingVoucher] !== updatedVoucher[key as keyof typeof updatedVoucher];
    });

    if (!hasChanges && !hasAuditChanges) {
      return state; // No change
    }

    // Force update for audit field changes to ensure real-time tab updates
    return {
      vouchers: state.vouchers.map(voucher =>
        voucher.id === voucherId ? updatedVoucher : voucher
      )
    };
  });
},

// COMPREHENSIVE SOLUTION: Clear all cached data and force refresh
clearCacheAndRefresh: async () => {
  console.log('🔄 CLEARING ALL CACHED DATA AND FORCING REFRESH...');

  // Clear localStorage cache
  try {
    localStorage.removeItem('voucher-management-system');
    console.log('✅ Cleared localStorage cache');
  } catch (error) {
    console.warn('⚠️ Could not clear localStorage:', error);
  }

  // Reset store state
  set({
    vouchers: [],
    voucherBatches: [],
    notifications: []
  });

  // Force fresh data fetch
  const { fetchVouchers, fetchBatches, fetchNotifications, currentUser } = get();

  if (currentUser?.department) {
    console.log(`🔄 Fetching fresh data for department: ${currentUser.department}`);
    await fetchVouchers(currentUser.department);
    await fetchBatches();
    await fetchNotifications();
    console.log('✅ Fresh data loaded successfully');
  }

  return true;
},

// NEW: Function to add existing voucher to store (for WebSocket)
addVoucherToStore: (voucher: Voucher) => {
  console.log('🔄 STORE: Adding existing voucher to store via WebSocket:', voucher.voucherId || voucher.id);

  set((state) => {
    // Check if voucher already exists to prevent duplicates
    const existingVoucher = state.vouchers.find(v =>
      (v.voucherId && v.voucherId === voucher.voucherId) ||
      (v.id && v.id === voucher.id) ||
      (v.voucher_id && v.voucher_id === voucher.voucher_id)
    );

    if (existingVoucher) {
      console.log('🔄 STORE: Voucher already exists, updating instead of adding:', voucher.voucherId || voucher.id);
      // ARCHITECTURAL FIX: Update existing voucher with merge strategy
      const updatedVouchers = state.vouchers.map(v => {
        if ((v.voucherId && v.voucherId === voucher.voucherId) ||
            (v.id && v.id === voucher.id) ||
            (v.voucher_id && v.voucher_id === voucher.voucher_id)) {
          return {
            ...v,
            ...voucher,
            // Preserve critical fields that shouldn't be overwritten
            id: v.id,
            voucherId: v.voucherId || voucher.voucherId || voucher.voucher_id
          };
        }
        return v;
      });

      return {
        ...state,
        vouchers: updatedVouchers,
        lastUpdate: Date.now(),
        version: (state.version || 0) + 1
      };
    }

    // ARCHITECTURAL FIX: Normalize voucher data before adding
    const normalizedVoucher = {
      ...voucher,
      voucherId: voucher.voucherId || voucher.voucher_id,
      originalDepartment: voucher.originalDepartment || voucher.original_department,  // 🔧 CRITICAL FIX: Add missing originalDepartment normalization
      createdBy: voucher.createdBy || voucher.created_by,
      dispatchedBy: voucher.dispatchedBy || voucher.dispatched_by || '',
      sentToAudit: Boolean(voucher.sentToAudit || voucher.sent_to_audit),
      deleted: Boolean(voucher.deleted),
      isReturned: Boolean(voucher.isReturned || voucher.is_returned),
      pendingReturn: Boolean(voucher.pendingReturn || voucher.pending_return),
      dispatched: Boolean(voucher.dispatched)
    };

    const updatedVouchers = [...state.vouchers, normalizedVoucher];
    console.log(`✅ VOUCHER ADDED TO STORE VIA WEBSOCKET: ${normalizedVoucher.voucherId || normalizedVoucher.id}`);
    console.log(`📊 Total vouchers in store: ${updatedVouchers.length}`);
    console.log(`🔍 Pending vouchers for ${normalizedVoucher.department}:`,
      updatedVouchers.filter(v =>
        v.department === normalizedVoucher.department &&
        (v.status === "PENDING SUBMISSION" || v.status === "PENDING") &&
        !v.sentToAudit &&
        !v.deleted
      ).length
    );

    // ARCHITECTURAL FIX: Comprehensive state update with change detection
    return {
      ...state,
      vouchers: updatedVouchers,
      lastUpdate: Date.now(),
      version: (state.version || 0) + 1,
      forceUpdate: Math.random() // Force component re-renders
    };
  });

  // ARCHITECTURAL FIX: Immediate UI update trigger
  setTimeout(() => {
    console.log('🔄 WEBSOCKET STORE: Voucher addition complete, triggering UI update');
    // Force all components to re-render by updating the store
    const currentState = get();
    set({
      ...currentState,
      lastUpdate: Date.now(),
      version: (currentState.version || 0) + 1,
      forceUpdate: Math.random()
    });
  }, 10); // Immediate update
},

});