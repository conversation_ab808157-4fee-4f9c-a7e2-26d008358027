import { useState } from 'react';
import { Voucher } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { formatCurrentDate } from '@/lib/store/utils';
import { toast } from 'sonner';

export const useVoucherEditing = () => {
  const updateVoucher = useAppStore((state) => state.updateVoucher);
  const addProvisionalCashRecord = useAppStore((state) => state.addProvisionalCashRecord);
  const loadProvisionalCashRecords = useAppStore((state) => state.loadProvisionalCashRecords);
  const vouchers = useAppStore((state) => state.vouchers);
  const currentUser = useAppStore((state) => state.currentUser);

  const [voucherEdits, setVoucherEdits] = useState<Record<string, Partial<any>>>({});
  const [showReturnCommentMap, setShowReturnCommentMap] = useState<Record<string, boolean>>({});

  const handleVoucherEdit = (voucherId: string, field: string, value: any) => {
    console.log(`Editing voucher ${voucherId}, field: ${field}, value:`, value);

    // Special handling for isReturned field
    if (field === 'isReturned') {
      // If setting to true, show the comment dialog
      if (value === true) {
        setShowReturnCommentMap(prev => ({...prev, [voucherId]: true}));
      }
    }

    // Handle null field - clear edits
    if (field === null) {
      setVoucherEdits(prev => {
        const newEdits = { ...prev };
        delete newEdits[voucherId];
        return newEdits;
      });
      return;
    }

    // Process comment fields - ensure they're strings
    if (field === 'comment' || field === 'returnComment') {
      if (value !== undefined && value !== null) {
        value = String(value);
      }
    }

    setVoucherEdits(prev => ({
      ...prev,
      [voucherId]: {
        ...(prev[voucherId] || {}),
        [field]: value
      }
    }));
  };

  const handleSaveVoucherEdits = async (voucherId: string) => {
    if (!voucherEdits[voucherId]) {
      console.log(`No edits found for voucher ${voucherId}`);
      return;
    }

    console.log(`Saving edits for voucher ${voucherId}:`, voucherEdits[voucherId]);

    try {
      // Check for return voucher - needs special handling
      if (voucherEdits[voucherId].isReturned === true) {
        // Get return comment
        const returnComment = voucherEdits[voucherId].returnComment;

        // Only proceed if a comment was provided
        if (!returnComment || returnComment.trim() === '') {
          toast.error('PLEASE PROVIDE A RETURN COMMENT', {
            duration: 3000,
          });
          return;
        }

        // Ensure returnComment is a string and trimmed
        const cleanComment = String(returnComment).trim();

        console.log(`Processing return for voucher ${voucherId} with comment: "${cleanComment}"`);
        handleMarkForReturn(voucherId, cleanComment);
      }

      // Make a copy of the edits to ensure we don't modify the original
      const editsToApply = { ...voucherEdits[voucherId] };

      // CRITICAL FIX: Set workStarted flag when any work is done on the voucher
      // This moves the voucher from NEW VOUCHERS to PENDING DISPATCH
      if (editsToApply.preAuditedAmount !== undefined ||
          editsToApply.preAuditedBy ||
          editsToApply.certifiedBy ||
          editsToApply.pendingReturn ||
          editsToApply.postProvisionalCash) {
        editsToApply.workStarted = true;
      }

      // Ensure critical fields are set
      // If status is not explicitly set, default to AUDIT PROCESSING when key fields are edited
      if (!editsToApply.status &&
          (editsToApply.preAuditedAmount !== undefined ||
           editsToApply.preAuditedBy ||
           editsToApply.certifiedBy)) {
        editsToApply.status = "AUDIT PROCESSING";
      }

      // Ensure it's not marked as dispatched
      if (editsToApply.status === "AUDIT PROCESSING") {
        editsToApply.dispatched = false;
      }

      // Ensure preAuditedAmount is a number if provided
      if (editsToApply.preAuditedAmount !== undefined) {
        if (typeof editsToApply.preAuditedAmount === 'string') {
          editsToApply.preAuditedAmount = parseFloat(editsToApply.preAuditedAmount);
        }
      }

      // Ensure taxAmount is a number if provided
      if (editsToApply.taxAmount !== undefined && typeof editsToApply.taxAmount === 'string') {
        editsToApply.taxAmount = parseFloat(editsToApply.taxAmount);
      }

      // CRITICAL FIX: Always set workStarted flag when saving edits in audit
      if (editsToApply.preAuditedAmount !== undefined ||
          editsToApply.preAuditedBy ||
          editsToApply.certifiedBy ||
          editsToApply.comment ||
          editsToApply.postProvisionalCash) {
        editsToApply.workStarted = true;
      }

      // Log final edits before applying
      console.log('Final edits being applied:', editsToApply);

      // Regular voucher update
      updateVoucher(voucherId, editsToApply);

      // PRODUCTION FIX: Force immediate UI refresh for PENDING DISPATCH tab
      setTimeout(() => {
        // Trigger a second update to force re-render
        updateVoucher(voucherId, { ...editsToApply });
        console.log(`🔄 AUDIT EDIT: Forced UI refresh for voucher ${voucherId}`);
      }, 50);

      // Check if provisional cash record needs to be created
      if (editsToApply.postProvisionalCash) {
        const voucher = vouchers.find(v => v.id === voucherId);
        if (voucher) {
          try {
            await addProvisionalCashRecord({
              voucherId: voucher.id,
              voucherRef: voucher.voucherId,
              claimant: voucher.claimant,
              description: voucher.description,
              mainAmount: voucher.preAuditedAmount || voucher.amount,
              currency: voucher.currency,
              date: formatCurrentDate()
            });
            console.log('✅ Provisional cash record created successfully');

            // REAL-TIME FIX: Immediately refresh provisional cash records
            try {
              await loadProvisionalCashRecords();
              console.log('✅ Provisional cash records refreshed in real-time');

              // Show success notification
              toast('✅ Provisional cash record created successfully', {
                description: `Record created for ${voucher.claimant} - ${voucher.voucherId}`,
                duration: 4000,
              });
            } catch (refreshError) {
              console.error('❌ Failed to refresh provisional cash records:', refreshError);
            }
          } catch (error) {
            console.error('❌ Failed to create provisional cash record:', error);
            toast('Failed to create provisional cash record', {
              description: 'Please try again or contact support.',
            });
          }
        }
      }

      // Clear edits after saving
      setVoucherEdits(prev => {
        const newEdits = { ...prev };
        delete newEdits[voucherId];
        return newEdits;
      });

      // Reset return comment dialog state
      setShowReturnCommentMap(prev => {
        const newMap = { ...prev };
        delete newMap[voucherId];
        return newMap;
      });

      // Verification: log the voucher state after update
      setTimeout(() => {
        const voucher = vouchers.find(v => v.id === voucherId);
        if (voucher) {
          console.log(`Verification - Voucher ${voucherId} after save:`, {
            id: voucher.id,
            status: voucher.status,
            preAuditedAmount: voucher.preAuditedAmount,
            preAuditedBy: voucher.preAuditedBy,
            certifiedBy: voucher.certifiedBy
          });
        }
      }, 50);

    } catch (error) {
      console.error('Error saving voucher edits:', error);
      toast.error('Failed to save voucher edits', {
        duration: 3000,
      });
    }
  };

  const handleMarkForReturn = (voucherId: string, comment: string) => {
    const voucher = vouchers.find(v => v.id === voucherId);
    if (!voucher) {
      console.error(`Voucher ${voucherId} not found for return`);
      return;
    }

    const currentTime = formatCurrentDate();

    // Ensure comment is a string and trimmed
    const cleanComment = String(comment).trim();

    // Only proceed if a comment was provided
    if (cleanComment === '') {
      toast.error('PLEASE PROVIDE A RETURN COMMENT', {
        duration: 3000,
      });
      return;
    }

    console.log(`Marking voucher ${voucherId} for return with comment: "${cleanComment}"`);

    // Update the existing voucher with return status and comment
    const updateData: Partial<Voucher> = {
      // Always include preAuditedAmount
      preAuditedAmount: voucher.preAuditedAmount || voucher.amount,
      // Always include preAuditedBy and certifiedBy if they exist
      preAuditedBy: voucher.preAuditedBy || currentUser?.name,
      certifiedBy: voucher.certifiedBy || currentUser?.name,
      // Return specific fields
      pendingReturn: true,
      isReturned: false,  // Will be set to true when dispatched
      returnComment: cleanComment,
      comment: cleanComment,
      status: "AUDIT PROCESSING",  // Keep in PENDING DISPATCH until dispatched
      returnTime: currentTime,
      dispatched: false,    // Ensure it's not dispatched yet
      // CRITICAL FIX: Set workStarted flag to ensure it appears in PENDING DISPATCH
      workStarted: true
    };

    // Update the voucher in the store
    console.log(`Updating voucher ${voucherId} with return data:`, updateData);
    updateVoucher(voucherId, updateData);

    // Verify the update was applied
    setTimeout(() => {
      const updatedVoucher = vouchers.find(v => v.id === voucherId);
      if (updatedVoucher) {
        console.log(`After marking for return, voucher ${voucherId} state:`, {
          preAuditedAmount: updatedVoucher.preAuditedAmount,
          preAuditedBy: updatedVoucher.preAuditedBy,
          certifiedBy: updatedVoucher.certifiedBy,
          pendingReturn: updatedVoucher.pendingReturn,
          isReturned: updatedVoucher.isReturned,
          status: updatedVoucher.status,
          dispatched: updatedVoucher.dispatched
        });
      }
    }, 100);

    toast.success('VOUCHER MARKED FOR RETURN', {
      duration: 3000,
    });

    return 'pending-dispatch'; // Switch to PENDING DISPATCH tab
  };

  // LEGACY REJECTION CODE REMOVED - USE VoucherWorkflowManager COMPONENT INSTEAD
  const handleMarkForRejection = (voucherId: string, comment: string) => {
    console.error('❌ LEGACY REJECTION CALLED - This should not happen!');
    console.error('Use VoucherWorkflowManager component for all rejection operations');
    throw new Error('Legacy rejection code disabled - use VoucherWorkflowManager');
  };

  const handleReturnToNew = (voucherId: string) => {
    const updateData: Partial<Voucher> = {
      preAuditedAmount: undefined,
      taxType: undefined,
      taxAmount: undefined,
      postProvisionalCash: undefined,
      preAuditedBy: undefined,
      certifiedBy: undefined,
      pendingReturn: false,
      returnComment: undefined,
      workStarted: false  // CRITICAL FIX: Reset workStarted to move voucher back to NEW VOUCHERS tab
    };

    updateVoucher(voucherId, updateData);

    toast.success('VOUCHER RETURNED TO NEW VOUCHERS', {
      duration: 3000,
    });
  };

  return {
    voucherEdits,
    setVoucherEdits,
    showReturnCommentMap,
    setShowReturnCommentMap,
    handleVoucherEdit,
    handleSaveVoucherEdits,
    handleMarkForReturn,
    handleMarkForRejection,
    handleReturnToNew
  };
};
