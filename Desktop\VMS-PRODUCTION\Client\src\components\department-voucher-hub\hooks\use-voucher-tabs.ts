
import { useState, useEffect } from 'react';
import { Department, Voucher } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { VOUCHER_STATUSES } from '@/lib/constants/voucher-statuses';

export const useVoucherTabs = (department: Department) => {
  const vouchers = useAppStore((state) => state.vouchers);
  const fetchVouchers = useAppStore((state) => state.fetchVouchers);
  const [activeTab, setActiveTab] = useState('new-vouchers');

  // Fetch vouchers on department change - add fetchVouchers to dependencies to prevent stale closure
  useEffect(() => {
    fetchVouchers('ALL'); // Fetch ALL vouchers, then filter by originalDepartment in frontend
  }, [department, fetchVouchers]);

  // Removed problematic useEffect calls that were causing infinite loops

  // SEPARATE WORKFLOW PATHS: NEW VOUCHER tab (Normal + Resubmitted, NO Rejection Copies)
  const newVouchers = vouchers.filter(v => {
    // For audit dashboard viewing department hubs (e.g., FINANCE VOUCHER HUB)
    const check1 = v.originalDepartment === department;
    const check2 = v.department === 'AUDIT';
    const check3 = v.status === VOUCHER_STATUSES.AUDIT_PROCESSING;
    const check4 = v.receivedByAudit === true;
    const check5 = !v.dispatched;
    const check6 = !v.deleted;

    const basicCriteria = check1 && check2 && check3 && check4 && check5 && check6;

    // WORKFLOW SEPARATION: Identify voucher types
    const isNormalVoucher = !v.isRejectionCopy && (v.resubmissionCount === 0 || !v.resubmissionCount);
    const isResubmittedVoucher = v.resubmissionCount && v.resubmissionCount > 0 && !v.isRejectionCopy;
    const isRejectionCopy = v.isRejectionCopy === true || v.isRejectionCopy === 1;

    // NEW VOUCHER tab: Only normal and resubmitted vouchers (NO rejection copies)
    // Rejection copies go directly to PENDING DISPATCH
    return basicCriteria && v.workStarted !== true && (isNormalVoucher || isResubmittedVoucher) && !isRejectionCopy;
  });

  // SEPARATE WORKFLOW PATHS: PENDING DISPATCH tab (Normal + Resubmitted + Rejection Copies)
  const pendingDispatchVouchers = vouchers.filter(v => {
    // Base criteria for all vouchers in PENDING DISPATCH
    const baseCriteria = v.originalDepartment === department &&
                        v.department === 'AUDIT' &&
                        !v.dispatched &&
                        !v.deleted;

    if (!baseCriteria) return false;

    // WORKFLOW SEPARATION: Three distinct paths

    // PATH 1: Normal vouchers with work started
    const isNormalVoucherReady = v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
                                v.receivedByAudit === true &&
                                v.workStarted === true &&
                                !v.isRejectionCopy &&
                                (v.resubmissionCount === 0 || !v.resubmissionCount);

    // PATH 2: Resubmitted vouchers with work started
    const isResubmittedVoucherReady = v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
                                     v.receivedByAudit === true &&
                                     v.workStarted === true &&
                                     v.resubmissionCount && v.resubmissionCount > 0 &&
                                     !v.isRejectionCopy;

    // PATH 3: ✅ PERMANENT FIX - Rejected vouchers pending dispatch
    const isRejectedVoucherReady = v.rejectionType === 'DISPATCHABLE_COPY' &&
                                  (v.isRejectionCopy === true || v.isRejectionCopy === 1) &&
                                  v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
                                  (v.rejectionWorkflowStage === 'PENDING_DISPATCH' ||
                                   v.rejectionWorkflowStage === 'REJECTED_DUAL');

    return isNormalVoucherReady || isResubmittedVoucherReady || isRejectedVoucherReady;
  });
  // SEPARATE WORKFLOW PATHS: DISPATCHED tab (Normal + Resubmitted workflows ONLY)
  // PERMANENT RULE: DISPATCHED tab shows ONLY CERTIFIED vouchers (successful workflows)
  // PERMANENT RULE: REJECTED vouchers appear ONLY in REJECTED tab (never DISPATCHED)
  // PERMANENT RULE: Clean separation - no overlap between DISPATCHED and REJECTED tabs
  const dispatchedVouchers = vouchers.filter(v => {
    const isOriginalDepartment = v.originalDepartment === department;
    const isDispatchedByAudit = v.auditDispatchedBy && v.auditDispatchTime;
    const isNotDeleted = !v.deleted;

    // 🚨 NUCLEAR SAFEGUARDS: ABSOLUTELY NO REJECTED VOUCHERS IN DISPATCHED TAB
    const isAbsolutelyNotRejected = v.status !== VOUCHER_STATUSES.VOUCHER_REJECTED &&
                                   !v.isRejected &&
                                   !v.rejectedBy &&
                                   !v.rejectionType &&
                                   !v.isRejectionCopy &&
                                   !v.rejectionWorkflowStage;

    // WORKFLOW SEPARATION: ONLY TWO types can appear in DISPATCHED tab
    const isNormalVoucher = !v.isRejectionCopy && (v.resubmissionCount === 0 || !v.resubmissionCount);
    const isResubmittedVoucher = v.resubmissionCount && v.resubmissionCount > 0 && !v.isRejectionCopy;

    const isValidWorkflow = isNormalVoucher || isResubmittedVoucher;

    if (department === 'AUDIT') {
      // AUDIT DISPATCHED TAB: Show vouchers dispatched FROM Audit TO other departments
      const isInAuditDepartment = v.department === 'AUDIT';
      const isDispatched = v.dispatched === true;

      // AUDIT DISPATCHED TAB: Show ONLY CERTIFIED vouchers (normal workflow completed)
      // Rejected vouchers have permanent records in REJECTED tab - no need to duplicate
      const hasValidStatus = v.status === VOUCHER_STATUSES.VOUCHER_CERTIFIED;

      // NUCLEAR SAFEGUARD: Absolutely NO rejected vouchers in AUDIT DISPATCHED tab
      const isNotRejectedVoucher = v.status !== VOUCHER_STATUSES.VOUCHER_REJECTED;

      return isOriginalDepartment &&
             isDispatchedByAudit &&
             isInAuditDepartment &&
             isDispatched &&
             hasValidStatus &&
             isNotRejectedVoucher &&
             isAbsolutelyNotRejected &&
             isNotDeleted &&
             isValidWorkflow;
    } else {
      // DEPARTMENT DISPATCHED TAB: Show ONLY CERTIFIED vouchers that came back from audit
      const isDispatched = v.dispatched === true;

      // CRITICAL FIX: Only show CERTIFIED vouchers in DISPATCHED tab
      // Rejected vouchers should ONLY appear in REJECTED tab
      const hasValidStatus = v.status === VOUCHER_STATUSES.VOUCHER_CERTIFIED;

      // NUCLEAR SAFEGUARD: Absolutely NO rejected vouchers in DISPATCHED tab
      const isNotRejectedVoucher = v.status !== VOUCHER_STATUSES.VOUCHER_REJECTED;

      return isOriginalDepartment &&
             isDispatchedByAudit &&
             isDispatched &&
             hasValidStatus &&
             isNotRejectedVoucher &&
             isAbsolutelyNotRejected &&
             isNotDeleted &&
             isValidWorkflow;
    }
  });



  // ✅ PERMANENT FIX: REJECTED tab (Clean, Simple Logic)
  const rejectedVouchers = vouchers.filter(v => {
    const isRejectedStatus = v.status === VOUCHER_STATUSES.VOUCHER_REJECTED;
    const isNotDeleted = !v.deleted;

    if (!isRejectedStatus || !isNotDeleted) {
      return false;
    }

    // 🎯 PERMANENT LOGIC: Based on actual database structure

    if (department === 'AUDIT') {
      // AUDIT REJECTED TAB: Show permanent rejected records for this department's voucher hub
      const isOriginalDepartment = v.originalDepartment === department;

      // Show ONLY permanent rejected records (original vouchers that were rejected)
      const isPermanentRecord = v.rejectionType === 'PERMANENT_RECORD' &&
                               v.department === 'AUDIT' &&
                               !v.isRejectionCopy;

      return isOriginalDepartment && isPermanentRecord;
    } else {
      // DEPARTMENT REJECTED TAB: Show rejected vouchers received by department
      const isOriginalDepartment = v.originalDepartment === department;
      const isReceivedByDepartment = v.department === department;

      // 🎯 PERMANENT LOGIC: Show rejected vouchers that were dispatched to this department
      const isDispatchedRejectedVoucher = v.rejectionWorkflowStage === 'FINANCE_RECEIVED' ||
                                         v.rejectionWorkflowStage === 'DISPATCHED_TO_FINANCE';

      // Show Finance permanent records (copies that were received)
      const isFinancePermanentRecord = v.rejectionType === 'FINANCE_PERMANENT_RECORD';

      return isOriginalDepartment &&
             isRejectedStatus &&
             isReceivedByDepartment &&
             (isDispatchedRejectedVoucher || isFinancePermanentRecord);
    }
  });

  // SEPARATE WORKFLOW PATHS: RETURNED tab (Normal workflow only)
  const returnedVouchers = vouchers.filter(v => {
    const isOriginalDepartment = v.originalDepartment === department;
    const isReturnedStatus = v.status === VOUCHER_STATUSES.VOUCHER_RETURNED;
    const isNotDeleted = !v.deleted;

    // WORKFLOW SEPARATION: Only normal vouchers (NO rejection copies or resubmitted)
    const isNormalVoucher = !v.isRejectionCopy && (v.resubmissionCount === 0 || !v.resubmissionCount);

    return isOriginalDepartment && isReturnedStatus && isNotDeleted && isNormalVoucher;
  });

  // Tab counts for debugging (removed console logging to prevent loops)

  return {
    activeTab,
    setActiveTab,
    newVouchers,
    pendingDispatchVouchers,
    dispatchedVouchers,
    returnedVouchers,
    rejectedVouchers
  };
};
