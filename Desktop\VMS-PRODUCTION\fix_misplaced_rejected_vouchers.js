const mysql = require('mysql2/promise');

async function fixMisplacedRejectedVouchers() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  console.log('🔍 STEP 1: FINDING REJECTED VOUCHERS IN WRONG TABS...');
  
  // Find rejected vouchers that are marked as dispatched (should be in REJECTED tab only)
  const findQuery = `
    SELECT id, voucher_id, status, dispatched, department, original_department, 
           audit_dispatched_by, audit_dispatch_time, rejected_by, rejection_time
    FROM vouchers 
    WHERE status = 'VOUCHER REJECTED' 
    AND dispatched = TRUE 
    AND deleted = FALSE
    ORDER BY audit_dispatch_time DESC
  `;
  
  const [misplacedVouchers] = await connection.execute(findQuery);
  
  console.log(`Found ${misplacedVouchers.length} rejected vouchers incorrectly marked as dispatched:`);
  misplacedVouchers.forEach((voucher, index) => {
    console.log(`${index + 1}. ${voucher.voucher_id} (ID: ${voucher.id})`);
    console.log(`   Status: ${voucher.status}`);
    console.log(`   Dispatched: ${voucher.dispatched}`);
    console.log(`   Department: ${voucher.department}`);
    console.log(`   Rejected by: ${voucher.rejected_by}`);
    console.log(`   Audit dispatched by: ${voucher.audit_dispatched_by}`);
    console.log('');
  });

  if (misplacedVouchers.length === 0) {
    console.log('✅ No misplaced rejected vouchers found!');
    await connection.end();
    return;
  }

  console.log('🔧 STEP 2: FIXING MISPLACED REJECTED VOUCHERS...');
  
  // Fix each misplaced voucher
  for (const voucher of misplacedVouchers) {
    console.log(`Fixing voucher ${voucher.voucher_id}...`);
    
    const fixQuery = `
      UPDATE vouchers 
      SET dispatched = FALSE,
          updated_at = NOW()
      WHERE id = ?
    `;
    
    await connection.execute(fixQuery, [voucher.id]);
    console.log(`✅ Fixed: ${voucher.voucher_id} - dispatched set to FALSE`);
  }

  console.log('🎯 STEP 3: VERIFICATION...');
  
  // Verify the fix
  const [verifyResults] = await connection.execute(findQuery);
  
  if (verifyResults.length === 0) {
    console.log('✅ SUCCESS: All rejected vouchers are now correctly configured!');
    console.log('✅ Rejected vouchers will now appear ONLY in REJECTED tab');
  } else {
    console.log(`❌ Still found ${verifyResults.length} misplaced vouchers - manual intervention needed`);
  }

  await connection.end();
}

fixMisplacedRejectedVouchers().catch(console.error);
