import { v4 as uuidv4 } from 'uuid';
import { query, getTransaction } from '../database/db.js';
import { logger } from '../utils/logger.js';
import { VOUCHER_STATUSES, synchronizeVoucherFlags } from '../utils/voucherStatusFlow.js';
import { simpleEventBus } from '../events/simpleEventBus.js';

export interface VoucherWorkflowResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export interface RejectionRequest {
  voucherId: string;
  rejectedBy: string;
  rejectionReason: string;
  department: string;
}

export interface ResubmissionRequest {
  voucherId: string;
  resubmittedBy: string;
  resubmissionComment?: string;
  department: string;
}

export class VoucherWorkflowService {
  
  /**
   * COMPREHENSIVE VOUCHER REJECTION WORKFLOW
   * Handles the complete dual-tab rejection process with audit trails
   */
  static async rejectVoucher(request: RejectionRequest): Promise<VoucherWorkflowResult> {
    const connection = await getTransaction();
    
    try {
      await connection.beginTransaction();
      
      const { voucherId, rejectedBy, rejectionReason, department } = request;
      
      // Step 1: Get the voucher to reject
      const [vouchers] = await connection.query('SELECT * FROM vouchers WHERE id = ? OR voucher_id = ?', [voucherId, voucherId]) as any[];
      
      if (!vouchers || vouchers.length === 0) {
        throw new Error(`Voucher ${voucherId} not found`);
      }
      
      const voucher = vouchers[0];
      
      // Step 2: Validate voucher can be rejected
      if (voucher.status === VOUCHER_STATUSES.VOUCHER_REJECTED) {
        throw new Error('Voucher is already rejected');
      }
      
      if (voucher.status === VOUCHER_STATUSES.VOUCHER_CERTIFIED) {
        throw new Error('Cannot reject a certified voucher');
      }
      
      // Step 3: Create permanent rejection record (stays in Audit REJECTED tab)
      const rejectionTime = new Date().toISOString().replace('T', ' ').substring(0, 19);
      const { flags: permanentFlags } = synchronizeVoucherFlags({ status: VOUCHER_STATUSES.VOUCHER_REJECTED });
      
      // Build rejection audit trail
      const rejectionAuditTrail = {
        rejected_by: rejectedBy,
        rejection_time: rejectionTime,
        rejection_reason: rejectionReason,
        original_status: voucher.status,
        original_department: voucher.department,
        workflow_stage: 'REFINED_4_PHASE_REJECTION',
        timestamp: new Date().toISOString()
      };
      
      // Phase 1a: Update original voucher to PENDING DISPATCH
      await connection.query(`
        UPDATE vouchers SET
          status = ?,
          rejected_by = ?,
          rejection_time = ?,
          comment = ?,
          rejection_type = 'ORIGINAL_REJECTED',
          rejection_workflow_stage = 'PENDING_DISPATCH',
          work_started = TRUE
        WHERE id = ?
      `, [
        'AUDIT: PROCESSING',
        rejectedBy,
        rejectionTime,
        rejectionReason,
        voucher.id
      ]);
      
      // Phase 1b: Create Finance permanent record in AUDIT department (Finance Voucher Hub)
      const financePermId = uuidv4();

      await connection.query(`
        INSERT INTO vouchers (
          id, voucher_id, date, claimant, description, amount, currency,
          department, original_department, status, rejection_type,
          parent_voucher_id, rejection_workflow_stage,
          rejected_by, rejection_time, comment, created_by, created_at,
          workflow_state
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        financePermId,
        voucher.voucher_id + '-COPY', // Keep existing logic to avoid duplicate voucher_id
        voucher.date,
        voucher.claimant,
        voucher.description,
        voucher.amount,
        voucher.currency,
        'AUDIT', // In AUDIT department (Finance Voucher Hub)
        voucher.original_department || voucher.department, // Original department
        'VOUCHER REJECTED',
        'FINANCE_PERMANENT_RECORD',
        voucher.id, // Link to original voucher
        'FINANCE_PERMANENT',
        rejectedBy,
        rejectionTime,
        rejectionReason,
        rejectedBy,
        new Date(),
        'REJECTED'
      ]);
      
      await connection.commit();
      
      // Step 2: Broadcast real-time updates
      simpleEventBus.emit('voucher:rejected', {
        voucherId: voucher.id,
        rejectedBy,
        rejectionReason,
        originalVoucherId: voucher.id,
        financePermId,
        timestamp: rejectionTime
      });

      logger.info(`Voucher ${voucher.voucher_id} rejected successfully by ${rejectedBy} using refined workflow`, {
        voucherId: voucher.id,
        rejectedBy,
        rejectionReason
      });

      return {
        success: true,
        message: 'Voucher rejected successfully with refined 4-phase workflow',
        data: {
          originalVoucherId: voucher.id,
          financePermId,
          rejectionTime,
          rejectedBy,
          rejectionReason
        }
      };
      
    } catch (error) {
      await connection.rollback();
      logger.error('Voucher rejection failed:', error);
      return {
        success: false,
        message: 'Failed to reject voucher',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      connection.release();
    }
  }
  
  /**
   * VOUCHER RESUBMISSION WORKFLOW
   * Handles resubmission of rejected vouchers with preserved voucher ID
   */
  static async resubmitVoucher(request: ResubmissionRequest): Promise<VoucherWorkflowResult> {
    const connection = await getTransaction();
    
    try {
      await connection.beginTransaction();
      
      const { voucherId, resubmittedBy, resubmissionComment } = request;
      
      // Get voucher details
      const [vouchers] = await connection.query(
        'SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE',
        [voucherId]
      ) as any[];
      
      if (!vouchers || vouchers.length === 0) {
        throw new Error(`Voucher with ID ${voucherId} not found`);
      }

      const voucher = vouchers[0];
      let originalVoucherId = voucherId;
      let originalVoucher = voucher;

      // Handle resubmission from a Finance rejection copy
      if (voucher.is_rejection_copy && voucher.parent_voucher_id) {
        logger.info(`Resubmitting from Finance rejection copy, retrieving original: ${voucher.parent_voucher_id}`);
        const [originals] = await connection.query(
          'SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', 
          [voucher.parent_voucher_id]
        ) as any[];
        
        if (!originals || originals.length === 0) {
          throw new Error(`Original voucher ${voucher.parent_voucher_id} not found for rejection copy`);
        }
        
        originalVoucher = originals[0];
        originalVoucherId = originalVoucher.id;
      }

      // Mark any rejection copies as inactive
      logger.info(`Marking rejection copies for ${originalVoucherId} as inactive`);
      await connection.query(
        `UPDATE vouchers SET deleted = TRUE, deletion_time = ? 
         WHERE parent_voucher_id = ? AND is_rejection_copy = TRUE AND id <> ?`,
        [new Date().toISOString(), originalVoucherId, voucherId]
      );

      // Update original voucher with resubmission data
      logger.info(`Updating original voucher ${originalVoucherId} with resubmission data`);

      // Generate resubmission history entry
      const resubmissionHistory = originalVoucher.resubmission_history 
        ? JSON.parse(originalVoucher.resubmission_history) 
        : [];
      
      const resubmissionTime = new Date().toISOString();
      const newResubmissionCount = (originalVoucher.resubmission_count || 0) + 1;
      const resubmissionBadge = `RESUBMITTED #${newResubmissionCount}`;
      
      resubmissionHistory.push({
        date: resubmissionTime,
        reason: resubmissionComment || 'No reason provided',
        rejectedBy: originalVoucher.rejected_by || 'Unknown',
        resubmittedBy: resubmittedBy
      });
      
      // Update the original voucher
      await connection.query(
        `UPDATE vouchers SET 
         status = ?, 
         workflow_state = 'RESUBMITTED',
         rejection_workflow_stage = 'RESUBMITTED',
         resubmission_count = ?,
         resubmission_history = ?,
         resubmission_badge = ?,
         last_resubmitted_by = ?,
         last_resubmission_date = ?,
         resubmission_comment = ?,
         is_rejected = FALSE,
         rejected_by = NULL,
         rejection_time = NULL,
         rejection_comment = NULL,
         rejection_type = NULL,
         department = 'AUDIT',
         sent_to_audit = TRUE,
         received_by_audit = TRUE
         WHERE id = ?`,
        [
          VOUCHER_STATUSES.AUDIT_PROCESSING,
          newResubmissionCount,
          JSON.stringify(resubmissionHistory),
          resubmissionBadge,
          resubmittedBy,
          resubmissionTime,
          resubmissionComment,
          originalVoucherId
        ]
      );
      
      // Create audit trail directly to avoid circular dependencies
      const auditId = uuidv4();
      const auditTimestamp = new Date().toISOString().slice(0, 19).replace('T', ' ');
      const auditDetails = JSON.stringify({
        resubmissionComment,
        resubmissionCount: newResubmissionCount
      });
      
      await connection.query(
        `INSERT INTO audit_logs 
         (id, timestamp, user_id, user_name, department, action, description, 
          resource_type, resource_id, details, ip_address, user_agent, severity)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          auditId,
          auditTimestamp,
          resubmittedBy,
          resubmittedBy, // Using userId as userName since we don't have the actual name
          'FINANCE',
          'RESUBMIT_VOUCHER',
          `Voucher ${originalVoucherId} was resubmitted`,
          'VOUCHER',
          originalVoucherId,
          auditDetails,
          'system',
          'system',
          'INFO'
        ]
      );
      
      await connection.commit();
      
      // Return the response
      return {
        success: true,
        message: `Voucher resubmitted successfully (${resubmissionBadge})`,
        data: {
          voucherId: originalVoucherId,
          resubmissionCount: newResubmissionCount,
          resubmissionBadge,
          resubmissionTime,
          resubmittedBy
        }
      };
      
    } catch (error) {
      await connection.rollback();
      logger.error('Voucher resubmission failed:', error);
      return {
        success: false,
        message: 'Failed to resubmit voucher',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      connection.release();
    }
  }
  
  /**
   * Get voucher workflow status and history
   */
  static async getVoucherWorkflowStatus(voucherId: string): Promise<VoucherWorkflowResult> {
    try {
      const [vouchers] = await query(`
        SELECT
          id, voucher_id, status, workflow_state, rejection_workflow_stage,
          resubmission_count, resubmission_badge, resubmission_history,
          rejection_audit_trail, resubmission_audit_trail,
          original_rejection_reason, original_rejected_by,
          last_resubmitted_by, last_resubmission_date
        FROM vouchers
        WHERE id = ? OR voucher_id = ?
      `, [voucherId, voucherId]) as any[];

      if (!vouchers || vouchers.length === 0) {
        throw new Error(`Voucher ${voucherId} not found`);
      }

      const voucher = vouchers[0];

      return {
        success: true,
        message: 'Voucher workflow status retrieved successfully',
        data: {
          voucherId: voucher.id,
          voucherNumber: voucher.voucher_id,
          currentStatus: voucher.status,
          workflowState: voucher.workflow_state,
          rejectionWorkflowStage: voucher.rejection_workflow_stage,
          resubmissionCount: voucher.resubmission_count || 0,
          resubmissionBadge: voucher.resubmission_badge,
          resubmissionHistory: voucher.resubmission_history ? JSON.parse(voucher.resubmission_history) : [],
          rejectionAuditTrail: voucher.rejection_audit_trail ? JSON.parse(voucher.rejection_audit_trail) : null,
          resubmissionAuditTrail: voucher.resubmission_audit_trail ? JSON.parse(voucher.resubmission_audit_trail) : null,
          originalRejectionReason: voucher.original_rejection_reason,
          originalRejectedBy: voucher.original_rejected_by,
          lastResubmittedBy: voucher.last_resubmitted_by,
          lastResubmissionDate: voucher.last_resubmission_date
        }
      };

    } catch (error) {
      logger.error('Failed to get voucher workflow status:', error);
      return {
        success: false,
        message: 'Failed to get voucher workflow status',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Certify a resubmitted voucher
   */
  static async certifyResubmittedVoucher(voucherId: string, certifiedBy: string): Promise<VoucherWorkflowResult> {
    const connection = await getTransaction();

    try {
      await connection.beginTransaction();

      // Get the resubmitted voucher
      const [vouchers] = await connection.query(`
        SELECT * FROM vouchers
        WHERE (id = ? OR voucher_id = ?)
        AND workflow_state = 'RESUBMITTED'
      `, [voucherId, voucherId]) as any[];

      if (!vouchers || vouchers.length === 0) {
        throw new Error(`Resubmitted voucher ${voucherId} not found`);
      }

      const voucher = vouchers[0];
      const certificationTime = new Date().toISOString().replace('T', ' ').substring(0, 19);

      // Update voucher to certified state
      await connection.query(`
        UPDATE vouchers SET
          status = ?,
          workflow_state = 'CERTIFIED_AFTER_RESUBMISSION',
          rejection_workflow_stage = 'RESUBMITTED_CERTIFIED',
          certified_by = ?,
          resubmission_status = 'CERTIFIED',
          department = ?
        WHERE id = ?
      `, [
        VOUCHER_STATUSES.VOUCHER_CERTIFIED,
        certifiedBy,
        voucher.original_department || voucher.department,
        voucher.id
      ]);

      await connection.commit();

      // Broadcast real-time updates
      simpleEventBus.emit('voucher:resubmission_certified', {
        voucherId: voucher.id,
        certifiedBy,
        timestamp: certificationTime
      });

      logger.info(`Resubmitted voucher ${voucher.voucher_id} certified by ${certifiedBy}`);

      return {
        success: true,
        message: 'Resubmitted voucher certified successfully',
        data: {
          voucherId: voucher.id,
          certifiedBy,
          certificationTime
        }
      };

    } catch (error) {
      await connection.rollback();
      logger.error('Failed to certify resubmitted voucher:', error);
      return {
        success: false,
        message: 'Failed to certify resubmitted voucher',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      connection.release();
    }
  }
}
